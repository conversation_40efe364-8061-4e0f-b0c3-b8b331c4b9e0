import warnings
from datetime import datetime, timedelta
from functools import reduce
from typing import TYPE_CHECKING

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    from freqtrade.persistence import Trade

warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1E_Enhanced_LongShort(IStrategy):
    """
    E0V1E 增强版多空策略

    策略核心思想：
    基于均值回归的5分钟短线多空策略，在多重技术指标确认的超卖/超买状态下入场，
    通过动态出场机制和严格的风险管理来获取短期反弹/回调收益。

    主要特性：
    1. 支持做多和做空双向交易
    2. 统一的入场逻辑，消除代码重复
    3. 优化的风险管理和动态止损
    4. 增强的技术指标和成交量确认
    5. 智能的出场逻辑和风险控制
    6. 市场环境过滤机制
    7. 全面参数化，支持多空独立优化
    """

    # === 基础配置 ===
    minimal_roi = {
        "0": 1  # 禁用基于时间的ROI，完全依赖动态出场
    }
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300
    can_short = False  # 启用做空功能

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.12  # 统一止损水平

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.003
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    enable_long_trading = BooleanParameter(default=True, space="buy", optimize=False)
    enable_short_trading = BooleanParameter(default=True, space="sell", optimize=False)
    enable_market_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_volume_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_atr_filter = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    market_24h_change_min = DecimalParameter(
        -25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True
    )
    market_24h_change_max = DecimalParameter(
        5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=True
    )

    # ATR波动性过滤
    atr_min_pct = DecimalParameter(0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=True)
    atr_max_pct = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=True)

    # 成交量过滤
    volume_factor = DecimalParameter(1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=True)

    # === 做多入场信号参数 ===
    buy_rsi_fast = IntParameter(20, 50, default=35, space="buy", optimize=True)
    buy_rsi = IntParameter(15, 45, default=30, space="buy", optimize=True)
    buy_rsi_slow_trend = BooleanParameter(default=True, space="buy", optimize=False)
    buy_sma_ratio = DecimalParameter(
        0.92, 0.99, default=0.96, decimals=3, space="buy", optimize=True
    )
    buy_cti = DecimalParameter(-1.0, 0.5, default=-0.2, decimals=2, space="buy", optimize=True)

    # === 备用做多入场信号参数 ===
    enable_alt_long_entry = BooleanParameter(default=True, space="buy", optimize=False)
    buy_rsi_fast_alt = IntParameter(25, 45, default=34, space="buy", optimize=True)
    buy_rsi_alt = IntParameter(20, 35, default=28, space="buy", optimize=True)
    buy_sma_ratio_alt = DecimalParameter(
        0.94, 0.98, default=0.96, decimals=3, space="buy", optimize=True
    )

    # === 做空入场信号参数 ===
    sell_rsi_fast = IntParameter(65, 85, default=75, space="sell", optimize=True)
    sell_rsi = IntParameter(55, 75, default=65, space="sell", optimize=True)
    sell_rsi_slow_trend = BooleanParameter(default=True, space="sell", optimize=False)
    sell_sma_ratio = DecimalParameter(
        1.01, 1.08, default=1.04, decimals=3, space="sell", optimize=True
    )
    sell_cti = DecimalParameter(-0.5, 1.0, default=0.2, decimals=2, space="sell", optimize=True)

    # === 备用做空入场信号参数 ===
    enable_alt_short_entry = BooleanParameter(default=True, space="sell", optimize=False)
    sell_rsi_fast_alt = IntParameter(70, 90, default=80, space="sell", optimize=True)
    sell_rsi_alt = IntParameter(60, 80, default=70, space="sell", optimize=True)
    sell_sma_ratio_alt = DecimalParameter(
        1.02, 1.06, default=1.04, decimals=3, space="sell", optimize=True
    )

    # === 做多出场信号参数 ===
    sell_fastk_profit = IntParameter(70, 95, default=84, space="sell", optimize=True)
    sell_cci_loss = IntParameter(60, 100, default=80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # === 做空出场信号参数 ===
    buy_fastk_profit = IntParameter(5, 30, default=16, space="buy", optimize=True)
    buy_cci_loss = IntParameter(-100, -60, default=-80, space="buy", optimize=True)
    buy_rsi_bottom = IntParameter(15, 35, default=25, space="buy", optimize=True)

    # 时间止损参数
    max_hold_hours = IntParameter(4, 12, default=8, space="sell", optimize=True)
    time_exit_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True
    )

    # 利润保护参数
    profit_protection_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True
    )
    profit_protection_fastk_long = IntParameter(65, 85, default=75, space="sell", optimize=True)
    profit_protection_fastk_short = IntParameter(15, 35, default=25, space="buy", optimize=True)

    @property
    def protections(self):
        """交易保护机制"""
        return [
            {
                "method": "CooldownPeriod",
                "stop_duration_candles": 48,
            },
            {
                "method": "StoplossGuard",
                "lookback_period_candles": 24,
                "trade_limit": 2,
                "stop_duration_candles": 60,
                "only_per_pair": True,
            },
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义多空入场信号"""
        long_conditions = []
        short_conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.enable_market_filter.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.enable_atr_filter.value:
            atr_filter = (dataframe["atr_pct"] > self.atr_min_pct.value) & (
                dataframe["atr_pct"] < self.atr_max_pct.value
            )

        # === 成交量过滤器 ===
        volume_filter = True
        if self.enable_volume_filter.value:
            volume_filter = dataframe["volume_ratio"] > self.volume_factor.value

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # === 做多入场信号 ===
        if self.enable_long_trading.value:
            # 主做多信号：统一的超卖反弹逻辑
            rsi_trend_condition = True
            if self.buy_rsi_slow_trend.value:
                rsi_trend_condition = dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1)

            main_long_entry = (
                base_filter
                & rsi_trend_condition
                & (dataframe["rsi_fast"] < self.buy_rsi_fast.value)
                & (dataframe["rsi"] > self.buy_rsi.value)
                & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio.value)
                & (dataframe["cti"] < self.buy_cti.value)
                & (dataframe["bb_percent"] < 0.2)  # 接近布林带下轨
            )

            long_conditions.append(main_long_entry)
            dataframe.loc[main_long_entry, "enter_tag"] += "main_long"

            # 备用做多信号：更激进的抄底
            if self.enable_alt_long_entry.value:
                alt_long_entry = (
                    base_filter
                    & (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
                    & (dataframe["rsi_fast"] < self.buy_rsi_fast_alt.value)
                    & (dataframe["rsi"] > self.buy_rsi_alt.value)
                    & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio_alt.value)
                    & (dataframe["cti"] < self.buy_cti.value)
                    & (dataframe["close"] < dataframe["bb_lowerband"])  # 突破布林带下轨
                )

                long_conditions.append(alt_long_entry)
                dataframe.loc[alt_long_entry, "enter_tag"] += "alt_long"

        # === 做空入场信号 ===
        if self.enable_short_trading.value:
            # 主做空信号：统一的超买回调逻辑
            rsi_trend_condition_short = True
            if self.sell_rsi_slow_trend.value:
                rsi_trend_condition_short = dataframe["rsi_slow"] > dataframe["rsi_slow"].shift(1)

            main_short_entry = (
                base_filter
                & rsi_trend_condition_short
                & (dataframe["rsi_fast"] > self.sell_rsi_fast.value)
                & (dataframe["rsi"] < self.sell_rsi.value)
                & (dataframe["close"] > dataframe["sma_15"] * self.sell_sma_ratio.value)
                & (dataframe["cti"] > self.sell_cti.value)
                & (dataframe["bb_percent"] > 0.8)  # 接近布林带上轨
            )

            short_conditions.append(main_short_entry)
            dataframe.loc[main_short_entry, "enter_tag"] += "main_short"

            # 备用做空信号：更激进的做空
            if self.enable_alt_short_entry.value:
                alt_short_entry = (
                    base_filter
                    & (dataframe["rsi_slow"] > dataframe["rsi_slow"].shift(1))
                    & (dataframe["rsi_fast"] > self.sell_rsi_fast_alt.value)
                    & (dataframe["rsi"] < self.sell_rsi_alt.value)
                    & (dataframe["close"] > dataframe["sma_15"] * self.sell_sma_ratio_alt.value)
                    & (dataframe["cti"] > self.sell_cti.value)
                    & (dataframe["close"] > dataframe["bb_upperband"])  # 突破布林带上轨
                )

                short_conditions.append(alt_short_entry)
                dataframe.loc[alt_short_entry, "enter_tag"] += "alt_short"

        # === 应用入场条件 ===
        if long_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, long_conditions), "enter_long"] = 1

        if short_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, short_conditions), "enter_short"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态多空出场逻辑
        根据不同的入场原因和交易方向实现智能出场
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # 获取交易方向
        is_short = trade.is_short if hasattr(trade, "is_short") else False

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.max_hold_hours.value):
            if current_profit > self.time_exit_profit_threshold.value:
                return "exit_time_limit"

        # === 做多出场逻辑 ===
        if not is_short:
            # 盈利时的出场策略
            if current_profit > 0:
                # 高利润保护机制
                if current_profit > self.profit_protection_threshold.value:
                    if current_candle["fastk"] > self.profit_protection_fastk_long.value:
                        return "exit_long_profit_protection"

                # 标准盈利出场
                if current_candle["fastk"] > self.sell_fastk_profit.value:
                    return "exit_long_profit_fastk"

                # 针对主做多信号的特定出场
                if "main_long" in str(trade.enter_tag):
                    if current_candle["rsi"] > self.sell_rsi_peak.value:
                        return "exit_long_profit_rsi_peak"

                # 针对备用做多信号的特定出场
                if "alt_long" in str(trade.enter_tag):
                    # 布林带回归中轨出场
                    if current_rate > current_candle["bb_middleband"]:
                        return "exit_long_profit_bb_middle"

            # 风险控制出场
            # 小幅亏损时的保护性出场
            if current_profit > -0.05:
                if current_candle["cci"] > self.sell_cci_loss.value:
                    return "exit_long_risk_cci"

            # 趋势反转信号
            if current_profit > -0.03:
                if (
                    current_candle["rsi"] > 70
                    and current_candle["fastk"] > 80
                    and current_candle["fastd"] > 75
                ):
                    return "exit_long_risk_overbought"

        # === 做空出场逻辑 ===
        else:
            # 盈利时的出场策略
            if current_profit > 0:
                # 高利润保护机制
                if current_profit > self.profit_protection_threshold.value:
                    if current_candle["fastk"] < self.profit_protection_fastk_short.value:
                        return "exit_short_profit_protection"

                # 标准盈利出场
                if current_candle["fastk"] < self.buy_fastk_profit.value:
                    return "exit_short_profit_fastk"

                # 针对主做空信号的特定出场
                if "main_short" in str(trade.enter_tag):
                    if current_candle["rsi"] < self.buy_rsi_bottom.value:
                        return "exit_short_profit_rsi_bottom"

                # 针对备用做空信号的特定出场
                if "alt_short" in str(trade.enter_tag):
                    # 布林带回归中轨出场
                    if current_rate < current_candle["bb_middleband"]:
                        return "exit_short_profit_bb_middle"

            # 风险控制出场
            # 小幅亏损时的保护性出场
            if current_profit > -0.05:
                if current_candle["cci"] < self.buy_cci_loss.value:
                    return "exit_short_risk_cci"

            # 趋势反转信号
            if current_profit > -0.03:
                if (
                    current_candle["rsi"] < 30
                    and current_candle["fastk"] < 20
                    and current_candle["fastd"] < 25
                ):
                    return "exit_short_risk_oversold"

        # === 通用风险控制 ===
        # 波动性异常退出
        if current_candle["atr_pct"] > self.atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "exit_risk_high_volatility"

        # 成交量异常退出
        if current_candle["volume_ratio"] > 3.0:  # 异常放量
            if current_profit > -0.04:
                return "exit_risk_volume_spike"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_long", "exit_short", "exit_tag"]] = (0, 0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 多空交易的最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 根据交易方向进行额外检查
        if side == "long":
            # 做多时确保不在极端超买状态
            if current_candle["rsi"] > 80 and current_candle["fastk"] > 90:
                return False
        elif side == "short":
            # 做空时确保不在极端超卖状态
            if current_candle["rsi"] < 20 and current_candle["fastk"] < 10:
                return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整 - 支持多空不同杠杆策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础杠杆
        base_leverage = 2.0

        # 根据波动性调整杠杆
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 4.0:
                base_leverage = 1.5  # 高波动时降低杠杆
            elif atr_pct < 1.0:
                base_leverage = 3.0  # 低波动时可以适当提高杠杆

        # 根据入场类型调整杠杆
        if "alt_" in entry_tag:
            base_leverage *= 0.8  # 备用信号使用较低杠杆

        # 根据交易方向调整杠杆
        if side == "short":
            base_leverage *= 0.9  # 做空时稍微降低杠杆，因为风险通常更高

        return min(base_leverage, max_leverage)

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理 - 支持多空不同仓位策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_" in entry_tag:
            # 主信号使用标准仓位
            position_multiplier = 1.0
        elif "alt_" in entry_tag:
            # 备用信号使用较小仓位
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据交易方向调整仓位
        if side == "short":
            position_multiplier *= 0.85  # 做空时使用稍小的仓位

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 0.6  # 高波动时减小仓位
            elif atr_pct < 1.0:
                position_multiplier *= 1.2  # 低波动时可以适当增加仓位

        # 根据RSI极值调整仓位
        if "rsi" in current_candle:
            rsi = current_candle["rsi"]
            if side == "long" and rsi < 20:
                position_multiplier *= 1.1  # 极度超卖时做多可以稍微增加仓位
            elif side == "short" and rsi > 80:
                position_multiplier *= 1.1  # 极度超买时做空可以稍微增加仓位

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
