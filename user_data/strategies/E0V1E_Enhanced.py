import warnings
from datetime import datetime, timedelta
from functools import reduce

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1E_Enhanced(IStrategy):
    """
    E0V1E 增强版策略

    策略核心思想：
    基于均值回归的5分钟短线策略，在多重技术指标确认的超卖状态下入场，
    通过动态出场机制和严格的风险管理来获取短期反弹收益。

    主要优化：
    1. 统一入场逻辑，消除代码重复
    2. 优化风险管理，调整止损水平
    3. 增强技术指标，添加ATR和成交量确认
    4. 改进出场逻辑，实现动态风险控制
    5. 添加市场环境过滤机制
    6. 全面参数化，提高策略适应性
    """

    # === 基础配置 ===
    minimal_roi = {
        "0": 1  # 禁用基于时间的ROI，完全依赖动态出场
    }
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300  # 增加启动蜡烛数以支持24小时变化计算

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.12  # 从-25%优化到-12%，降低单笔风险

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.003  # 价格回撤0.3%时触发
    trailing_stop_positive_offset = 0.02  # 利润达到2%后激活
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    enable_market_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_volume_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_atr_filter = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    market_24h_change_min = DecimalParameter(
        -25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True
    )
    market_24h_change_max = DecimalParameter(
        5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=True
    )

    # ATR波动性过滤
    atr_min_pct = DecimalParameter(0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=True)
    atr_max_pct = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=True)

    # 成交量过滤
    volume_factor = DecimalParameter(1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=True)

    # === 主入场信号参数 ===
    buy_rsi_fast = IntParameter(20, 50, default=35, space="buy", optimize=True)
    buy_rsi = IntParameter(15, 45, default=30, space="buy", optimize=True)
    buy_rsi_slow_trend = BooleanParameter(default=True, space="buy", optimize=False)
    buy_sma_ratio = DecimalParameter(
        0.92, 0.99, default=0.96, decimals=3, space="buy", optimize=True
    )
    buy_cti = DecimalParameter(-1.0, 0.5, default=-0.2, decimals=2, space="buy", optimize=True)

    # === 备用入场信号参数 ===
    enable_alt_entry = BooleanParameter(default=True, space="buy", optimize=False)
    buy_rsi_fast_alt = IntParameter(25, 45, default=34, space="buy", optimize=True)
    buy_rsi_alt = IntParameter(20, 35, default=28, space="buy", optimize=True)
    buy_sma_ratio_alt = DecimalParameter(
        0.94, 0.98, default=0.96, decimals=3, space="buy", optimize=True
    )

    # === 出场信号参数 ===
    sell_fastk_profit = IntParameter(70, 95, default=84, space="sell", optimize=True)
    sell_cci_loss = IntParameter(60, 100, default=80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # 时间止损参数
    max_hold_hours = IntParameter(4, 12, default=8, space="sell", optimize=True)
    time_exit_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True
    )

    # 利润保护参数
    profit_protection_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True
    )
    profit_protection_fastk = IntParameter(65, 85, default=75, space="sell", optimize=True)

    @property
    def protections(self):
        """交易保护机制"""
        return [
            {
                "method": "CooldownPeriod",
                "stop_duration_candles": 48,  # 优化为4小时冷却期
            },
            {
                "method": "StoplossGuard",
                "lookback_period_candles": 24,  # 2小时回看期
                "trade_limit": 2,
                "stop_duration_candles": 60,  # 5小时保护期
                "only_per_pair": True,
            },
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义入场信号"""
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.enable_market_filter.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.enable_atr_filter.value:
            atr_filter = (dataframe["atr_pct"] > self.atr_min_pct.value) & (
                dataframe["atr_pct"] < self.atr_max_pct.value
            )

        # === 成交量过滤器 ===
        volume_filter = True
        if self.enable_volume_filter.value:
            volume_filter = dataframe["volume_ratio"] > self.volume_factor.value

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # === 主入场信号：统一的超卖反弹逻辑 ===
        rsi_trend_condition = True
        if self.buy_rsi_slow_trend.value:
            rsi_trend_condition = dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1)

        main_entry = (
            base_filter
            & rsi_trend_condition
            & (dataframe["rsi_fast"] < self.buy_rsi_fast.value)
            & (dataframe["rsi"] > self.buy_rsi.value)
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio.value)
            & (dataframe["cti"] < self.buy_cti.value)
            & (dataframe["bb_percent"] < 0.2)  # 接近布林带下轨
        )

        conditions.append(main_entry)
        dataframe.loc[main_entry, "enter_tag"] += "main_entry"

        # === 备用入场信号：更激进的抄底 ===
        if self.enable_alt_entry.value:
            alt_entry = (
                base_filter
                & (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
                & (dataframe["rsi_fast"] < self.buy_rsi_fast_alt.value)
                & (dataframe["rsi"] > self.buy_rsi_alt.value)
                & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio_alt.value)
                & (dataframe["cti"] < self.buy_cti.value)
                & (dataframe["close"] < dataframe["bb_lowerband"])  # 突破布林带下轨
            )

            conditions.append(alt_entry)
            dataframe.loc[alt_entry, "enter_tag"] += "alt_entry"

        # === 应用入场条件 ===
        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态出场逻辑
        根据不同的入场原因和市场状态实现智能出场
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.max_hold_hours.value):
            if current_profit > self.time_exit_profit_threshold.value:
                return "exit_time_limit"

        # === 盈利时的出场策略 ===
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.profit_protection_threshold.value:
                if current_candle["fastk"] > self.profit_protection_fastk.value:
                    return "exit_profit_protection"

            # 标准盈利出场
            if current_candle["fastk"] > self.sell_fastk_profit.value:
                return "exit_profit_fastk"

            # 针对主入场信号的特定出场
            if "main_entry" in str(trade.enter_tag):
                if current_candle["rsi"] > self.sell_rsi_peak.value:
                    return "exit_profit_rsi_peak"

            # 针对备用入场信号的特定出场
            if "alt_entry" in str(trade.enter_tag):
                # 布林带回归中轨出场
                if current_rate > current_candle["bb_middleband"]:
                    return "exit_profit_bb_middle"

        # === 风险控制出场 ===

        # 小幅亏损时的保护性出场
        if current_profit > -0.05:
            if current_candle["cci"] > self.sell_cci_loss.value:
                return "exit_risk_cci"

        # 趋势反转信号
        if current_profit > -0.03:
            if (
                current_candle["rsi"] > 70
                and current_candle["fastk"] > 80
                and current_candle["fastd"] > 75
            ):
                return "exit_risk_overbought"

        # 波动性异常退出
        if current_candle["atr_pct"] > self.atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "exit_risk_high_volatility"

        # 成交量异常退出
        if current_candle["volume_ratio"] > 3.0:  # 异常放量
            if current_profit > -0.04:
                return "exit_risk_volume_spike"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础杠杆
        base_leverage = 2.0

        # 根据波动性调整杠杆
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 4.0:
                base_leverage = 1.5  # 高波动时降低杠杆
            elif atr_pct < 1.0:
                base_leverage = 3.0  # 低波动时可以适当提高杠杆

        # 根据入场类型调整杠杆
        if "alt_entry" in entry_tag:
            base_leverage *= 0.8  # 备用信号使用较低杠杆

        return min(base_leverage, max_leverage)

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_entry" in entry_tag:
            # 主信号使用标准仓位
            position_multiplier = 1.0
        elif "alt_entry" in entry_tag:
            # 备用信号使用较小仓位
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 0.6  # 高波动时减小仓位
            elif atr_pct < 1.0:
                position_multiplier *= 1.2  # 低波动时可以适当增加仓位

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
