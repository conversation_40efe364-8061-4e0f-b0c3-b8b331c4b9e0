import warnings
from datetime import datetime, timedelta
from functools import reduce

import pandas_ta as pta
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter, RealParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1EN_Optimized(IStrategy):
    """
    这是 E0V1EN 策略的优化版本。
    核心思想: 在宏观风险可控的前提下，结合多种技术指标，捕捉短期超跌反弹的机会。

    相较于原版，主要优化点：
    1.  **新增入场逻辑**: 增加了基于布林带下轨的抄底信号，与原有的RSI抄底逻辑互补。
    2.  **精细化出场**: 离场逻辑与入场逻辑挂钩。RSI入场的交易会关注RSI高位离场，布林带入场的交易会关注回到中轨或上轨离场。
    3.  **动态时间止损**: 用基于ATR（平均真实波幅）的“僵尸交易”退出机制，取代了固定的时间止损，更智能地处理停滞的交易。
    4.  **代码结构优化**: 参数更清晰，修正了`startup_candle_count`，并增加了详细注释。
    """

    # --- 策略基础配置 ---
    minimal_roi = {
        "0": 100  # 完全禁用ROI，所有卖出由 custom_exit, stoploss, trailing_stop 控制
    }
    timeframe = "5m"
    process_only_new_candles = True
    # 策略最长lookback period是288 (24h_change_pct)，因此启动蜡烛数需要大于等于288+1
    startup_candle_count = 300

    # --- 订单配置 ---
    order_types = {
        "entry": "market",
        "exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
    }

    # --- 止损与止盈配置 ---
    stoploss = -0.25  # 固定止损

    trailing_stop = True  # 启用追踪止盈
    trailing_stop_positive = 0.005  # 当利润超过3.5%后，价格回撤0.5%则止盈
    trailing_stop_positive_offset = 0.035
    trailing_only_offset_is_reached = True

    # --- 可优化参数 ---

    # 宏观过滤器: 24小时价格变化范围
    buy_24h_change_min = DecimalParameter(-25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True)
    buy_24h_change_max = DecimalParameter(5.0, 50.0, default=20.0, decimals=1, space="buy", optimize=True)

    # 入场条件1: RSI回调买入
    buy_rsi_dip_rsi_fast = IntParameter(20, 45, default=40, space="buy", optimize=True)
    buy_rsi_dip_rsi = IntParameter(15, 40, default=30, space="buy", optimize=True)
    buy_rsi_dip_sma_ratio = DecimalParameter(0.900, 0.99, default=0.973, decimals=3, space="buy", optimize=True)
    buy_rsi_dip_cti = DecimalParameter(-0.9, 0, default=-0.5, decimals=2, space="buy", optimize=True)

    # 入场条件2: 布林带下轨抄底
    buy_bb_dip_rsi = IntParameter(20, 40, default=33, space="buy", optimize=True)
    buy_bb_dip_width = DecimalParameter(0.02, 0.15, default=0.06, decimals=3, space="buy", optimize=True)

    # 出场条件
    sell_fastk_profit = IntParameter(75, 100, default=85, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)
    sell_stale_trade_hours = IntParameter(2, 12, default=6, space="sell", optimize=True)  # 僵尸交易判断时长
    sell_stale_trade_profit = RealParameter(0.0, 0.03, default=0.005, space="sell", optimize=True)  # 僵尸交易利润阈值

    @property
    def protections(self):
        return [
            {
                "method": "CooldownPeriod",
                "stop_duration_candles": 48,  # 48 * 5m = 4小时冷却
            },
            {
                "method": "StoplossGuard",  # 防止在短时暴跌后立即买入
                "lookback_period_candles": 24,  # 2小时
                "trade_limit": 2,
                "stop_duration_candles": 60,  # 5小时
                "only_per_pair": True,
            },
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # --- 基础指标 ---
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # --- 随机指标 (用于出场) ---
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]

        # --- 24小时变化率 (宏观过滤器) ---
        dataframe["24h_change_pct"] = dataframe["close"].pct_change(periods=288) * 100

        # --- 布林带指标 ---
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_width"] = (dataframe["bb_upperband"] - dataframe["bb_lowerband"]) / dataframe["bb_middleband"]

        # --- ATR (用于僵尸交易检测) ---
        dataframe["atr_pct"] = (ta.ATR(dataframe, timeperiod=14) / dataframe["close"]) * 100

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # --- 宏观过滤器 ---
        macro_filter = (dataframe["24h_change_pct"].notna()) & (dataframe["24h_change_pct"] > self.buy_24h_change_min.value) & (dataframe["24h_change_pct"] < self.buy_24h_change_max.value)

        # --- 入场条件 1: RSI回调买入 (原策略逻辑优化) ---
        rsi_dip_condition = (
            (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
            & (dataframe["rsi_fast"] < self.buy_rsi_dip_rsi_fast.value)
            & (dataframe["rsi"] < self.buy_rsi_dip_rsi.value)
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_rsi_dip_sma_ratio.value)
            & (dataframe["cti"] < self.buy_rsi_dip_cti.value)
        )
        conditions.append(rsi_dip_condition & macro_filter)
        dataframe.loc[rsi_dip_condition, "enter_tag"] += "rsi_dip"

        # --- 入场条件 2: 布林带下轨抄底 ---
        bb_dip_condition = (
            (dataframe["close"] < dataframe["bb_lowerband"])
            & (dataframe["rsi"] < self.buy_bb_dip_rsi.value)  # 配合RSI确认超卖
            & (dataframe["bb_width"] > self.buy_bb_dip_width.value)  # 避免在通道极窄时入场
        )
        conditions.append(bb_dip_condition & macro_filter)
        dataframe.loc[bb_dip_condition, "enter_tag"] += "bb_dip"

        # --- 合并所有入场条件 ---
        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1

        return dataframe

    def custom_exit(self, pair: str, trade: "Trade", current_time: "datetime", current_rate: float, current_profit: float, **kwargs):
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # --- 盈利时的离场信号 ---
        if current_profit > 0:
            # 信号1: 随机指标过热 (通用止盈)
            if current_candle["fastk"] > self.sell_fastk_profit.value:
                return "exit_profit_fastk_peak"

            # 信号2: 如果是RSI抄底入场，则在RSI达到高位时离场
            if "rsi_dip" in trade.enter_tag:
                if current_candle["rsi"] > self.sell_rsi_peak.value:
                    return "exit_profit_rsi_peak"

            # 信号3: 如果是布林带抄底入场，则在价格回到中轨时离场
            if "bb_dip" in trade.enter_tag:
                if current_rate > current_candle["bb_middleband"]:
                    return "exit_profit_bb_middle"

        # --- 亏损或微利时的保护性离场信号 ---

        # 信号4: CCI指标反弹乏力 (通用保护)
        # 如果亏损不大，但CCI显示反弹可能结束，提前离场
        if current_profit > -0.05 and current_candle["cci"] > 100:
            return "exit_protection_cci"

        # 信号5: 僵尸交易离场 (动态时间止损)
        # 如果持仓超过N小时，利润微薄，且市场波动性很低，则认为交易已停滞
        if (
            (current_time - trade.open_date_utc > timedelta(hours=self.sell_stale_trade_hours.value))
            and (abs(current_profit) < self.sell_stale_trade_profit.value)
            and (current_candle["atr_pct"] < 0.8)
        ):  # ATR百分比小于0.8%认为波动性低
            return "exit_stale_trade"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # custom_exit 会处理所有逻辑，这里保持为空
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, None)
        return dataframe


# --- END OF FILE E0V1EN_Optimized.py ---
