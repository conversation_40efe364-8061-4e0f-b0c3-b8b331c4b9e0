{"strategy_name": "E0V1E_Enhanced", "description": "E0V1E增强版策略 - 全面优化的均值回归策略", "version": "1.0.0", "author": "AI优化版本", "timeframe": "5m", "params": {"trailing": {"trailing_stop": true, "trailing_stop_positive": 0.003, "trailing_stop_positive_offset": 0.02, "trailing_only_offset_is_reached": true}, "max_open_trades": {"max_open_trades": 50}, "buy": {"enable_market_filter": true, "enable_volume_filter": true, "enable_atr_filter": true, "enable_alt_entry": true, "buy_rsi_slow_trend": true, "market_24h_change_min": -18.0, "market_24h_change_max": 20.0, "atr_min_pct": 0.5, "atr_max_pct": 5.0, "volume_factor": 1.2, "buy_rsi_fast": 35, "buy_rsi": 30, "buy_sma_ratio": 0.96, "buy_cti": -0.2, "buy_rsi_fast_alt": 34, "buy_rsi_alt": 28, "buy_sma_ratio_alt": 0.96}, "sell": {"sell_fastk_profit": 84, "sell_cci_loss": 80, "sell_rsi_peak": 75, "max_hold_hours": 8, "time_exit_profit_threshold": -0.05, "profit_protection_threshold": 0.05, "profit_protection_fastk": 75}, "protection": {"cooldown_period": 48, "stoploss_guard_enabled": true, "stoploss_guard_lookback": 24, "stoploss_guard_trade_limit": 2, "stoploss_guard_stop_duration": 60}, "roi": {"0": 1}, "stoploss": {"stoploss": -0.12}}, "optimization_ranges": {"buy": {"market_24h_change_min": [-25.0, -5.0], "market_24h_change_max": [5.0, 30.0], "atr_min_pct": [0.3, 1.0], "atr_max_pct": [3.0, 8.0], "volume_factor": [1.0, 2.0], "buy_rsi_fast": [20, 50], "buy_rsi": [15, 45], "buy_sma_ratio": [0.92, 0.99], "buy_cti": [-1.0, 0.5], "buy_rsi_fast_alt": [25, 45], "buy_rsi_alt": [20, 35], "buy_sma_ratio_alt": [0.94, 0.98]}, "sell": {"sell_fastk_profit": [70, 95], "sell_cci_loss": [60, 100], "sell_rsi_peak": [65, 85], "max_hold_hours": [4, 12], "time_exit_profit_threshold": [-0.08, -0.02], "profit_protection_threshold": [0.03, 0.08], "profit_protection_fastk": [65, 85]}}, "features": {"unified_entry_logic": "消除了原策略中的重复代码，统一入场逻辑", "enhanced_risk_management": "从25%止损优化到12%，大幅降低单笔风险", "dynamic_exit_strategy": "根据入场原因实现不同的出场策略", "market_environment_filter": "添加24小时价格变化、ATR波动性、成交量过滤", "advanced_indicators": "新增布林带、ATR、成交量比率等指标", "position_sizing": "动态仓位管理，根据信号强度和波动性调整", "leverage_control": "智能杠杆调整，高波动时自动降低杠杆", "trade_confirmation": "最后一道风险控制，确保交易安全", "protection_mechanisms": "优化保护机制，减少冷却时间，增加止损保护"}, "improvements_over_original": {"code_quality": {"eliminated_duplication": "合并了buy_1和buy_new的重复逻辑", "better_naming": "更清晰的变量和函数命名", "comprehensive_documentation": "详细的注释和文档字符串"}, "risk_management": {"reduced_stoploss": "从-25%降至-12%", "time_based_exit": "添加基于时间的止损机制", "dynamic_protection": "根据市场状态动态调整保护水平"}, "signal_quality": {"market_filter": "添加市场环境过滤，避免极端市场条件", "volatility_filter": "ATR波动性过滤，确保适当的交易环境", "volume_confirmation": "成交量确认，提高信号可靠性"}, "exit_strategy": {"context_aware_exits": "根据入场原因选择相应出场策略", "profit_protection": "高利润时的保护机制", "risk_based_exits": "多层次风险控制出场"}}, "usage_recommendations": {"backtesting": {"timerange": "建议使用至少6个月的历史数据进行回测", "optimization": "使用hyperopt进行参数优化，建议优化周期为100-500次", "validation": "使用walk-forward分析验证策略稳定性"}, "live_trading": {"start_small": "建议先用小资金测试策略表现", "monitor_closely": "密切监控前几周的交易表现", "adjust_parameters": "根据实际表现微调参数"}, "risk_management": {"max_drawdown": "建议设置最大回撤限制为15-20%", "position_sizing": "单笔交易风险控制在总资金的1-2%", "diversification": "建议同时交易多个不相关的交易对"}}, "ft_stratparam_v": 1, "export_time": "2025-06-20 12:00:00.000000+00:00"}