import warnings
from functools import reduce
from typing import TYPE_CHECKING

import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    pass

warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1E_Enhanced_Optimized(IStrategy):
    """
    E0V1E 增强优化版策略（仅做多）

    策略核心思想：
    基于均值回归的5分钟短线做多策略，在多重技术指标确认的超卖状态下入场，
    通过动态出场机制和严格的风险管理来获取短期反弹收益。

    主要优化：
    1. 修复了布林带参数类型问题
    2. 优化了风险管理和动态止损
    3. 增强了技术指标和成交量确认
    4. 改进了出场逻辑和风险控制
    5. 完善了市场环境过滤机制
    6. 全面参数化，提高策略适应性
    7. 优化了杠杆和仓位管理逻辑
    """

    # === 基础配置 ===
    minimal_roi = {
        "0": 1  # 禁用基于时间的ROI，完全依赖动态出场
    }
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300
    can_short = False  # 仅做多

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.12  # 优化后的止损水平

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.003
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    enable_market_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_volume_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_atr_filter = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    market_24h_change_min = DecimalParameter(
        -25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True
    )
    market_24h_change_max = DecimalParameter(
        5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=True
    )

    # ATR波动性过滤
    atr_min_pct = DecimalParameter(0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=True)
    atr_max_pct = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=True)

    # 成交量过滤
    volume_factor = DecimalParameter(1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=True)

    # === 做多入场信号参数 ===
    buy_rsi_fast = IntParameter(20, 50, default=35, space="buy", optimize=True)
    buy_rsi = IntParameter(15, 45, default=30, space="buy", optimize=True)
    buy_rsi_slow_trend = BooleanParameter(default=True, space="buy", optimize=False)
    buy_sma_ratio = DecimalParameter(
        0.92, 0.99, default=0.96, decimals=3, space="buy", optimize=True
    )
    buy_cti = DecimalParameter(-1.0, 0.5, default=-0.2, decimals=2, space="buy", optimize=True)

    # === 备用做多入场信号参数 ===
    enable_alt_entry = BooleanParameter(default=True, space="buy", optimize=False)
    buy_rsi_fast_alt = IntParameter(25, 45, default=34, space="buy", optimize=True)
    buy_rsi_alt = IntParameter(20, 35, default=28, space="buy", optimize=True)
    buy_sma_ratio_alt = DecimalParameter(
        0.94, 0.98, default=0.96, decimals=3, space="buy", optimize=True
    )

    # === 出场信号参数 ===
    sell_fastk_profit = IntParameter(70, 95, default=84, space="sell", optimize=True)
    sell_cci_loss = IntParameter(60, 100, default=80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # 时间止损参数
    max_hold_hours = IntParameter(4, 12, default=8, space="sell", optimize=True)
    time_exit_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True
    )

    # 利润保护参数
    profit_protection_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True
    )
    profit_protection_fastk = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # === 新增优化参数 ===
    # 布林带入场确认
    bb_entry_threshold = DecimalParameter(
        0.1, 0.3, default=0.2, decimals=2, space="buy", optimize=True
    )
    bb_alt_entry_enabled = BooleanParameter(default=True, space="buy", optimize=False)

    # 动量确认参数
    momentum_confirmation = BooleanParameter(default=True, space="buy", optimize=False)
    momentum_threshold = DecimalParameter(
        -0.5, 0.0, default=-0.3, decimals=2, space="buy", optimize=True
    )

    @property
    def protections(self):
        """交易保护机制"""
        return [
            {
                "method": "CooldownPeriod",
                "stop_duration_candles": 48,
            },
            {
                "method": "StoplossGuard",
                "lookback_period_candles": 24,
                "trade_limit": 2,
                "stop_duration_candles": 60,
                "only_per_pair": True,
            },
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)
        dataframe["sma_50"] = ta.SMA(dataframe, timeperiod=50)  # 新增长期均线

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # 新增动量指标
        dataframe["roc"] = ta.ROC(dataframe, timeperiod=10)  # 变化率
        dataframe["mom"] = ta.MOM(dataframe, timeperiod=10)  # 动量

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # 新增成交量指标
        dataframe["volume_ema"] = ta.EMA(dataframe["volume"], timeperiod=10)
        dataframe["volume_trend"] = dataframe["volume_ema"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标（修复参数类型） ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )
        dataframe["bb_width"] = (dataframe["bb_upperband"] - dataframe["bb_lowerband"]) / dataframe[
            "bb_middleband"
        ]

        # === 新增技术指标 ===
        # MACD
        macd = ta.MACD(dataframe)
        dataframe["macd"] = macd["macd"]
        dataframe["macdsignal"] = macd["macdsignal"]
        dataframe["macdhist"] = macd["macdhist"]

        # Williams %R
        dataframe["willr"] = ta.WILLR(dataframe, timeperiod=14)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义优化的做多入场信号"""
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.enable_market_filter.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.enable_atr_filter.value:
            atr_filter = (dataframe["atr_pct"] > self.atr_min_pct.value) & (
                dataframe["atr_pct"] < self.atr_max_pct.value
            )

        # === 成交量过滤器（优化版） ===
        volume_filter = True
        if self.enable_volume_filter.value:
            volume_filter = (
                (dataframe["volume_ratio"] > self.volume_factor.value)
                & (dataframe["volume_trend"] > 0.95)  # 成交量趋势确认
            )

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # === 动量确认条件 ===
        momentum_filter = True
        if self.momentum_confirmation.value:
            momentum_filter = (
                (dataframe["roc"] > self.momentum_threshold.value)  # 变化率不能太负
                | (dataframe["macdhist"] > dataframe["macdhist"].shift(1))  # MACD柱状图改善
            )

        # === 主做多信号：优化的超卖反弹逻辑 ===
        rsi_trend_condition = True
        if self.buy_rsi_slow_trend.value:
            rsi_trend_condition = dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1)

        main_entry = (
            base_filter
            & momentum_filter
            & rsi_trend_condition
            & (dataframe["rsi_fast"] < self.buy_rsi_fast.value)
            & (dataframe["rsi"] > self.buy_rsi.value)
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio.value)
            & (dataframe["cti"] < self.buy_cti.value)
            & (dataframe["bb_percent"] < self.bb_entry_threshold.value)  # 可调节的布林带阈值
            & (dataframe["willr"] < -70)  # Williams %R 超卖确认
            & (dataframe["close"] > dataframe["sma_50"])  # 长期趋势过滤
        )

        conditions.append(main_entry)
        dataframe.loc[main_entry, "enter_tag"] += "main_optimized"

        # === 备用做多信号：更激进的抄底（优化版） ===
        if self.enable_alt_entry.value:
            alt_entry = (
                base_filter
                & (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
                & (dataframe["rsi_fast"] < self.buy_rsi_fast_alt.value)
                & (dataframe["rsi"] > self.buy_rsi_alt.value)
                & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio_alt.value)
                & (dataframe["cti"] < self.buy_cti.value)
                & (dataframe["willr"] < -80)  # 更严格的超卖
            )

            # 布林带突破条件（可选）
            if self.bb_alt_entry_enabled.value:
                alt_entry = alt_entry & (dataframe["close"] < dataframe["bb_lowerband"])
            else:
                alt_entry = alt_entry & (dataframe["bb_percent"] < 0.1)  # 非常接近下轨

            conditions.append(alt_entry)
            dataframe.loc[alt_entry, "enter_tag"] += "alt_optimized"

        # === 应用入场条件 ===
        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        优化的动态出场逻辑
        增强的风险管理和盈利保护机制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.max_hold_hours.value):
            if current_profit > self.time_exit_profit_threshold.value:
                return "exit_time_limit"

        # === 盈利时的出场策略（优化版） ===
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.profit_protection_threshold.value:
                if current_candle["fastk"] > self.profit_protection_fastk.value:
                    return "exit_profit_protection"

            # 标准盈利出场
            if current_candle["fastk"] > self.sell_fastk_profit.value:
                return "exit_profit_fastk"

            # 针对主入场信号的特定出场
            if "main_optimized" in str(trade.enter_tag):
                if current_candle["rsi"] > self.sell_rsi_peak.value:
                    return "exit_profit_rsi_peak"

                # MACD顶背离出场
                if (
                    current_candle["macd"] < current_candle["macdsignal"]
                    and current_candle["macdhist"] < 0
                ):
                    return "exit_profit_macd_divergence"

            # 针对备用入场信号的特定出场
            if "alt_optimized" in str(trade.enter_tag):
                # 布林带回归中轨出场
                if current_rate > current_candle["bb_middleband"]:
                    return "exit_profit_bb_middle"

                # Williams %R 超买出场
                if current_candle["willr"] > -20:
                    return "exit_profit_willr_overbought"

            # 新增：动量衰减出场
            if current_profit > 0.02:  # 利润超过2%时
                if current_candle["roc"] < -1.0 and current_candle["mom"] < current_candle[
                    "mom"
                ].shift(1):
                    return "exit_profit_momentum_fade"

        # === 风险控制出场（优化版） ===

        # 小幅亏损时的保护性出场
        if current_profit > -0.05:
            if current_candle["cci"] > self.sell_cci_loss.value:
                return "exit_risk_cci"

            # 新增：布林带宽度收缩出场（波动性降低）
            if current_candle["bb_width"] < 0.02:  # 布林带收缩
                return "exit_risk_low_volatility"

        # 趋势反转信号（增强版）
        if current_profit > -0.03:
            if (
                current_candle["rsi"] > 70
                and current_candle["fastk"] > 80
                and current_candle["fastd"] > 75
                and current_candle["willr"] > -30  # 新增Williams %R确认
            ):
                return "exit_risk_overbought_confirmed"

        # 新增：跌破长期均线保护
        if current_profit > -0.06:
            if current_rate < current_candle["sma_50"]:
                return "exit_risk_below_sma50"

        # === 通用风险控制（优化版） ===

        # 波动性异常退出
        if current_candle["atr_pct"] > self.atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "exit_risk_high_volatility"

        # 成交量异常退出（优化版）
        if (
            current_candle["volume_ratio"] > 3.0 and current_candle["volume_trend"] > 1.5
        ):  # 异常放量且趋势确认
            if current_profit > -0.04:
                return "exit_risk_volume_spike"

        # 新增：MACD死叉保护
        if current_profit > -0.04:
            if current_candle["macd"] < current_candle["macdsignal"] and current_candle[
                "macdhist"
            ] < current_candle["macdhist"].shift(1):
                return "exit_risk_macd_bearish"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        优化的交易确认函数 - 增强的最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = [
            "rsi",
            "rsi_fast",
            "atr_pct",
            "volume_ratio",
            "willr",
            "macd",
            "bb_width",
        ]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 新增：确保不在极端超买状态入场
        if current_candle["rsi"] > 80 and current_candle["fastk"] > 90:
            return False

        # 新增：确保布林带宽度足够（避免在极低波动时交易）
        if current_candle["bb_width"] < 0.01:
            return False

        # 新增：确保成交量不异常
        if current_candle["volume_ratio"] > 5.0:  # 避免在异常放量时入场
            return False

        # 新增：MACD确认（避免在明显下跌趋势中入场）
        if (
            "main_optimized" in entry_tag
            and current_candle["macd"] < current_candle["macdsignal"]
            and current_candle["macdhist"] < -0.001
        ):
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        优化的动态杠杆调整
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础杠杆
        base_leverage = 2.0

        # 根据波动性调整杠杆（优化版）
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 4.0:
                base_leverage = 1.5  # 高波动时降低杠杆
            elif atr_pct > 6.0:
                base_leverage = 1.2  # 极高波动时进一步降低
            elif atr_pct < 1.0:
                base_leverage = 3.0  # 低波动时可以适当提高杠杆

        # 根据入场类型调整杠杆
        if "alt_optimized" in entry_tag:
            base_leverage *= 0.8  # 备用信号使用较低杠杆
        elif "main_optimized" in entry_tag:
            base_leverage *= 1.0  # 主信号使用标准杠杆

        # 新增：根据市场强度调整杠杆
        if "rsi" in current_candle and "willr" in current_candle:
            # 超卖程度越深，可以适当提高杠杆
            if current_candle["rsi"] < 25 and current_candle["willr"] < -80:
                base_leverage *= 1.1
            elif current_candle["rsi"] > 35:  # RSI不够低时降低杠杆
                base_leverage *= 0.9

        # 新增：根据布林带位置调整杠杆
        if "bb_percent" in current_candle:
            if current_candle["bb_percent"] < 0.1:  # 非常接近下轨
                base_leverage *= 1.05
            elif current_candle["bb_percent"] > 0.3:  # 不够接近下轨
                base_leverage *= 0.95

        return min(base_leverage, max_leverage)

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        优化的动态仓位管理
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_optimized" in entry_tag:
            # 主信号使用标准仓位
            position_multiplier = 1.0
        elif "alt_optimized" in entry_tag:
            # 备用信号使用较小仓位
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据市场波动性调整仓位（优化版）
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 0.6  # 高波动时减小仓位
            elif atr_pct > 7.0:
                position_multiplier *= 0.4  # 极高波动时大幅减小仓位
            elif atr_pct < 1.0:
                position_multiplier *= 1.2  # 低波动时可以适当增加仓位

        # 新增：根据RSI极值调整仓位
        if "rsi" in current_candle and "willr" in current_candle:
            # 超卖程度越深，可以适当增加仓位
            if current_candle["rsi"] < 20 and current_candle["willr"] < -85:
                position_multiplier *= 1.15  # 极度超卖时增加仓位
            elif current_candle["rsi"] < 25 and current_candle["willr"] < -80:
                position_multiplier *= 1.1  # 深度超卖时稍微增加仓位
            elif current_candle["rsi"] > 35:
                position_multiplier *= 0.9  # RSI不够低时减小仓位

        # 新增：根据布林带位置调整仓位
        if "bb_percent" in current_candle:
            if current_candle["bb_percent"] < 0.05:  # 非常接近下轨
                position_multiplier *= 1.1
            elif current_candle["bb_percent"] < 0.1:  # 接近下轨
                position_multiplier *= 1.05
            elif current_candle["bb_percent"] > 0.25:  # 不够接近下轨
                position_multiplier *= 0.9

        # 新增：根据成交量趋势调整仓位
        if "volume_trend" in current_candle:
            if current_candle["volume_trend"] > 1.2:  # 成交量上升趋势
                position_multiplier *= 1.05
            elif current_candle["volume_trend"] < 0.8:  # 成交量下降趋势
                position_multiplier *= 0.95

        # 新增：根据MACD状态调整仓位
        if "macdhist" in current_candle:
            if current_candle["macdhist"] > 0:  # MACD柱状图为正
                position_multiplier *= 1.05
            elif current_candle["macdhist"] < -0.001:  # MACD柱状图明显为负
                position_multiplier *= 0.9

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
