#!/bin/bash
# shellcheck disable=SC1091
cd "$(dirname "$0")" || exit
source .env
source /home/<USER>/freqtrade/.venv/bin/activate

freqtrade download-data \
    --datadir ${DATA_DIR} \
    --data-format-ohlcv feather \
    --data-format-trades feather \
    --timerange=${START_DATE}- \
    --pairs-file ${DATA_DIR}/pairs.json \
    --exchange binance \
    --timeframe 5m 15m 30m 1h 8h $1

# if [ $# -lt 1 ]; then
#     /bin/bash "$0" --prepend
# fi


    # --config ${CONFIG_FILE} \
