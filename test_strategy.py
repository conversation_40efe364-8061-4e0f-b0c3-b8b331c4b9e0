#!/usr/bin/env python3
"""
测试策略加载脚本
用于验证E0V1E_Enhanced_LongShort策略是否能正常加载和运行
"""

import sys

import numpy as np
import pandas as pd

# 添加策略路径
sys.path.append("user_data/strategies")

try:
    from E0V1E_Enhanced_Optimized import E0V1E_Enhanced_Optimized

    print("✅ 策略导入成功!")

    # 创建策略实例
    strategy = E0V1E_Enhanced_Optimized()
    print("✅ 策略实例化成功!")

    # 创建测试数据
    dates = pd.date_range("2024-01-01", periods=1000, freq="5min")
    np.random.seed(42)

    # 生成模拟的OHLCV数据
    close_prices = 100 + np.cumsum(np.random.randn(1000) * 0.1)
    high_prices = close_prices + np.random.rand(1000) * 2
    low_prices = close_prices - np.random.rand(1000) * 2
    open_prices = close_prices + np.random.randn(1000) * 0.5
    volumes = np.random.randint(1000, 10000, 1000)

    test_dataframe = pd.DataFrame(
        {
            "date": dates,
            "open": open_prices,
            "high": high_prices,
            "low": low_prices,
            "close": close_prices,
            "volume": volumes,
        }
    )

    print("✅ 测试数据创建成功!")

    # 测试指标计算
    try:
        indicators_df = strategy.populate_indicators(test_dataframe.copy(), {"pair": "BTC/USDT"})
        print("✅ 技术指标计算成功!")
        print(f"   - 数据行数: {len(indicators_df)}")
        print(f"   - 指标列数: {len(indicators_df.columns)}")

        # 检查关键指标是否存在
        key_indicators = [
            "rsi",
            "rsi_fast",
            "rsi_slow",
            "sma_15",
            "ema_20",
            "cti",
            "cci",
            "fastk",
            "fastd",
            "atr",
            "atr_pct",
            "volume_sma",
            "volume_ratio",
            "24h_change_pct",
            "bb_lowerband",
            "bb_middleband",
            "bb_upperband",
            "bb_percent",
        ]

        missing_indicators = [ind for ind in key_indicators if ind not in indicators_df.columns]
        if missing_indicators:
            print(f"⚠️  缺失指标: {missing_indicators}")
        else:
            print("✅ 所有关键指标都已计算!")

    except Exception as e:
        print(f"❌ 指标计算失败: {e}")
        sys.exit(1)

    # 测试入场信号
    try:
        entry_df = strategy.populate_entry_trend(indicators_df.copy(), {"pair": "BTC/USDT"})
        print("✅ 入场信号计算成功!")

        # 检查入场信号
        long_signals = entry_df["enter_long"].sum() if "enter_long" in entry_df.columns else 0
        short_signals = entry_df["enter_short"].sum() if "enter_short" in entry_df.columns else 0

        print(f"   - 做多信号数量: {long_signals}")
        print(f"   - 做空信号数量: {short_signals}")

    except Exception as e:
        print(f"❌ 入场信号计算失败: {e}")
        sys.exit(1)

    # 测试出场信号
    try:
        exit_df = strategy.populate_exit_trend(entry_df.copy(), {"pair": "BTC/USDT"})
        print("✅ 出场信号计算成功!")

    except Exception as e:
        print(f"❌ 出场信号计算失败: {e}")
        sys.exit(1)

    print("\n🎉 策略测试完成! 所有功能正常工作!")
    print("\n📊 策略配置摘要:")
    print(f"   - 时间框架: {strategy.timeframe}")
    print(f"   - 支持做空: {strategy.can_short}")
    print(f"   - 止损水平: {strategy.stoploss}")
    print(f"   - 追踪止损: {strategy.trailing_stop}")
    print(f"   - 启动蜡烛数: {strategy.startup_candle_count}")

except ImportError as e:
    print(f"❌ 策略导入失败: {e}")
    print("请确保freqtrade环境已正确安装")
    sys.exit(1)
except Exception as e:
    print(f"❌ 策略测试失败: {e}")
    sys.exit(1)
